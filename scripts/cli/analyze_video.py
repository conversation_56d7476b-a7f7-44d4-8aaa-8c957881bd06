#!/usr/bin/env python3
"""
Command-line interface for basketball video analysis.
Provides direct video analysis without the API layer.
"""

import asyncio
import argparse
import json
import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import time

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.pipeline import BasketballAnalysisPipeline, ProcessingConfig, ProcessingStage
from src.utils.video_io.video_reader import VideoReader
from app.core.config import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProgressTracker:
    """Progress tracking for CLI interface."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.start_time = time.time()
        self.stage_times = {}
    
    def update_progress(self, stage: str, progress: float, message: str = ""):
        """Update and display progress."""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # Store stage timing
        if stage not in self.stage_times:
            self.stage_times[stage] = current_time
        
        # Create progress bar
        bar_length = 40
        filled_length = int(bar_length * progress)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        # Display progress
        print(f"\r[{bar}] {progress:.1%} | {stage} | {elapsed:.1f}s | {message}", end='', flush=True)
        
        if progress >= 1.0:
            print()  # New line when complete
        
        if self.verbose and message:
            logger.info(f"Stage: {stage}, Progress: {progress:.1%}, Message: {message}")


def create_argument_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Basketball Video Analysis CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic analysis
  python analyze_video.py input.mp4 --output-dir ./results

  # Custom confidence thresholds
  python analyze_video.py input.mp4 --player-confidence 0.6 --ball-confidence 0.4

  # Disable team classification
  python analyze_video.py input.mp4 --no-team-classification

  # Generate only JSON stats
  python analyze_video.py input.mp4 --no-video --no-tactical-view

  # S3 input
  python analyze_video.py s3://bucket/video.mp4 --aws-region us-west-2
        """
    )
    
    # Input/Output arguments
    parser.add_argument(
        "input_video",
        help="Path to input video file or S3 URL (s3://bucket/key)"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        default="./output",
        help="Output directory for results (default: ./output)"
    )
    
    parser.add_argument(
        "--job-id",
        type=str,
        help="Custom job ID (default: auto-generated)"
    )
    
    # Detection confidence settings
    parser.add_argument(
        "--player-confidence",
        type=float,
        default=0.5,
        help="Player detection confidence threshold (0.0-1.0, default: 0.5)"
    )
    
    parser.add_argument(
        "--ball-confidence",
        type=float,
        default=0.3,
        help="Ball detection confidence threshold (0.0-1.0, default: 0.3)"
    )
    
    parser.add_argument(
        "--court-confidence",
        type=float,
        default=0.6,
        help="Court detection confidence threshold (0.0-1.0, default: 0.6)"
    )
    
    # Processing options
    parser.add_argument(
        "--no-team-classification",
        action="store_true",
        help="Disable team classification"
    )
    
    parser.add_argument(
        "--team-colors",
        nargs="+",
        help="Specify team colors (e.g., 'red jersey' 'blue jersey')"
    )
    
    parser.add_argument(
        "--batch-size",
        type=int,
        default=20,
        help="Batch size for processing (default: 20)"
    )
    
    # Output options
    parser.add_argument(
        "--no-video",
        action="store_true",
        help="Skip annotated video generation"
    )
    
    parser.add_argument(
        "--no-tactical-view",
        action="store_true",
        help="Skip tactical view generation"
    )
    
    parser.add_argument(
        "--no-json",
        action="store_true",
        help="Skip JSON stats export"
    )
    
    # Performance options
    parser.add_argument(
        "--no-gpu",
        action="store_true",
        help="Force CPU-only processing"
    )
    
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable intermediate result caching"
    )
    
    # AWS options (for S3 input)
    parser.add_argument(
        "--aws-access-key-id",
        type=str,
        help="AWS access key ID (for S3 input)"
    )
    
    parser.add_argument(
        "--aws-secret-access-key",
        type=str,
        help="AWS secret access key (for S3 input)"
    )
    
    parser.add_argument(
        "--aws-region",
        type=str,
        default="us-east-1",
        help="AWS region (default: us-east-1)"
    )
    
    # Logging options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress progress output"
    )
    
    parser.add_argument(
        "--log-file",
        type=str,
        help="Log file path"
    )
    
    return parser


def validate_arguments(args: argparse.Namespace) -> None:
    """Validate command-line arguments."""
    # Validate confidence thresholds
    for conf_name, conf_value in [
        ("player-confidence", args.player_confidence),
        ("ball-confidence", args.ball_confidence),
        ("court-confidence", args.court_confidence)
    ]:
        if not 0.0 <= conf_value <= 1.0:
            raise ValueError(f"{conf_name} must be between 0.0 and 1.0")
    
    # Validate batch size
    if args.batch_size < 1:
        raise ValueError("batch-size must be at least 1")
    
    # Validate input video
    input_path = args.input_video
    if not input_path.startswith("s3://"):
        if not Path(input_path).exists():
            raise FileNotFoundError(f"Input video not found: {input_path}")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)


def setup_logging(args: argparse.Namespace) -> None:
    """Setup logging configuration."""
    log_level = logging.DEBUG if args.verbose else logging.INFO
    if args.quiet:
        log_level = logging.WARNING
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    if not args.quiet:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler
    if args.log_file:
        file_handler = logging.FileHandler(args.log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)


async def main():
    """Main CLI function."""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    try:
        # Setup logging
        setup_logging(args)
        
        # Validate arguments
        validate_arguments(args)
        
        logger.info("Starting basketball video analysis")
        logger.info(f"Input: {args.input_video}")
        logger.info(f"Output directory: {args.output_dir}")
        
        # Create processing configuration
        config = ProcessingConfig(
            player_detection_confidence=args.player_confidence,
            ball_detection_confidence=args.ball_confidence,
            court_detection_confidence=args.court_confidence,
            team_classification_enabled=not args.no_team_classification,
            team_colors=args.team_colors,
            generate_annotated_video=not args.no_video,
            generate_tactical_view=not args.no_tactical_view,
            export_json_stats=not args.no_json,
            batch_size=args.batch_size,
            use_gpu=not args.no_gpu,
            cache_intermediate_results=not args.no_cache
        )
        
        # Initialize pipeline
        pipeline = BasketballAnalysisPipeline(config)
        
        # Setup progress tracking
        if not args.quiet:
            progress_tracker = ProgressTracker(verbose=args.verbose)
            pipeline.set_progress_callback(progress_tracker.update_progress)
        
        # Generate job ID
        job_id = args.job_id or f"cli_{int(time.time())}"
        
        # Process video
        logger.info("Starting video processing...")
        result = await pipeline.process_video(
            video_source=args.input_video,
            job_id=job_id,
            output_dir=Path(args.output_dir)
        )
        
        # Display results
        print(f"\n✅ Analysis completed successfully!")
        print(f"Job ID: {result.job_id}")
        print(f"Processing time: {result.processing_time:.2f} seconds")
        print(f"Total frames: {result.total_frames}")
        print(f"Video duration: {result.duration:.2f} seconds")
        
        # Detection results
        print(f"\n📊 Detection Results:")
        print(f"  Players detected: {len(result.player_detections)}")
        print(f"  Ball detections: {len(result.ball_detections)}")
        print(f"  Court detections: {len(result.court_detections)}")
        
        # Event results
        print(f"\n🏀 Event Detection:")
        print(f"  Shots detected: {len(result.shots)}")
        print(f"  Passes detected: {len(result.passes)}")
        print(f"  Possessions: {len(result.possessions)}")
        
        # Output files
        print(f"\n📁 Output Files:")
        if result.annotated_video_path:
            print(f"  Annotated video: {result.annotated_video_path}")
        if result.tactical_view_path:
            print(f"  Tactical view: {result.tactical_view_path}")
        if result.json_stats_path:
            print(f"  JSON statistics: {result.json_stats_path}")
        
        # Errors
        if result.errors:
            print(f"\n⚠️  Warnings/Errors:")
            for error in result.errors:
                print(f"  - {error}")
        
        logger.info("Analysis completed successfully")
        return 0
        
    except KeyboardInterrupt:
        print("\n❌ Analysis interrupted by user")
        logger.info("Analysis interrupted by user")
        return 1
        
    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        logger.error(f"Analysis failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
