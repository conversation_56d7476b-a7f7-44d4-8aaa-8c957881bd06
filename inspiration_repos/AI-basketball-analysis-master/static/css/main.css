body {
  background-color:#F0E5DE;
  font-family: Righteous;
  padding: 0;
  margin: 0;
}

.navbar-brand {
    display: flex;
    flex-direction: row;
}

.navBrand {
    padding-top: 0.2vw;
    padding-left: 0.5vw;
    font-weight: 700;
}

nav .navbar-nav li a {
    margin: 0 3vw;
    font-size: 0.9vw;
    font-weight: 600;
}

.upload-image-thumb {
  max-width: 45vw;
}

.landing-container {
    font-family: Righteous;
    font-weight: 550;
    overflow-x: hidden;
    background-color: #F0E5DE;
    width: 100%;
    /* height: 100vh; */
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    align-items: center;
}

.main-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    background-color: #7C7877;
}

.titleandbutton {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
}

.title {
    font-size: 4.8vw;
}

.titleinfo {
    font-size: 1.8vw;
    font-weight: 530;
}

.wave-button {
    background-image: url(../img/wave.svg);
    background-size: cover;
    width: 100%;
    height: 7vw;
    margin-top: -0.1vw;
}

.container-fluid {
    padding: 0;
    margin: 0;
}

.features-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: -2vw;
}

.feature-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-bottom: 1vw;
    width: 100%;
}

.small-feature {
    /* width: 50%; */
}

.feature-image {
    border: 5px solid #7C7877;
}

.feature-title {
    font-size: 2vw;
}

.feature-text {
    font-size: 1.5vw;
}

.demo-feature {
    width: 90vw;
}

.demo-small-feature {
    width: 44vw;
}

.comparison {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    margin-top: 0.5vw;
}

.image-container {
    display: flex;
    flex-direction: column;
}

.stat-container {
    width: 48vw;
    padding-bottom: 1vw;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    background-color: #f1bbba;
    border: 5px solid #7c7877;
    border-radius: 10px;
}

.result-stat-container {
    padding-bottom: 1vw;
    display: flex;
    flex-direction: row;
    background-color: #f1bbba;
    border: 5px solid #7c7877;
    border-radius: 10px;
}

.stat-row {
    display: flex;
    flex-direction: row;
    width: 50%;
    justify-content: space-around;
}

.stat-item {
    display: flex;
    flex-direction: column;
    padding: 0 2vw;
    margin: 0;
}

.statname-container {
    margin-top: 0.6vw;
}

.stat-name {
    font-family: Righteous;
    font-style: normal;
    font-weight: normal;
    font-size: 1.5vw;
    font-weight: 550;
    color: #7c7877;
}

.underline {
    width: 35%;
    height: 0.6vw;
    background-color: #eb9f9f;
    margin-top: -0.7vw;
    z-index: -1;
}

.stat-num {
    font-family: Righteous;
    font-style: normal;
    font-weight: bold;
    font-size: 2.5vw;
    color: #f0e5de;
    text-align: center;
    margin-top: 1vw;
    -webkit-text-stroke-width: 1.5px;
    -webkit-text-stroke-color: #7c7877;
}

.image-description {
    display: flex;
    flex-direction: column;
    margin-bottom: 1vw;
}

.image-stat {
    margin-top: 2vw;
    margin-bottom: 1vw;
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-evenly;
}

.image-underline {
    width: 100%;
}

.video-container {
    margin-top: 1vw;
    max-width: 95vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.video-description {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 2vw;
}

.video-underline {
    width: 100%;
}

.padding-right {
    padding-right: 2vw;
}

.right-description {
    margin-left: 40%;
}

.submit-result {
    width: 500px;
}

.submit-button {
    background-color: #e3504d;
    border: none;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    height: 12vw;
    width: 12vw;
    text-align: center;
    text-decoration: none;
    margin: 1.5vw 1vw 1.5vw 3vw;
    border-radius: 100%;
    font-size: 2.5vw;
    font-weight: bold;
    cursor: pointer;
    animation: shadow-pulse 1.25s infinite;
}

.submit-button:hover {
    background-color: #e22825;
    transform: scale(1.1);
    text-shadow: 0px 0px 6px rgba(255, 255, 255, 1);
    -webkit-box-shadow: 0px 5px 40px -10px rgba(0, 0, 0, 0.57);
    -moz-box-shadow: 0px 5px 40px -10px rgba(0, 0, 0, 0.57);
    transition: all 0.4s ease 0s;
}

@keyframes shadow-pulse {
    0% {
        box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
    }

    100% {
        box-shadow: 0 0 0 1.5vw rgba(0, 0, 0, 0);
    }
}

button {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.result-title {
    margin-left: 3vw;
    color: #7c7877;
    font-size: 2.5vw;
    font-weight: bold;
}

.hint {
    font-size: 0.7vw;
}

.small-stat {
    width:40vw;
}

.result-stat {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.row {
    width: 100%;
    padding-bottom: 1vw;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
}

.result-container {
    width: 100%;
}

.shot-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: row;
}

.trajectory-underline {
    width: 50%;
}

.shooting-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.landing-image {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.demo-image {
    margin-top: 1vw;
    width: 40vw;
}

.mainbutton {
    font-size: 2vw;
    padding: 1vw 5vw;
    border-radius: 10px;
    background-color: rgb(142, 184, 182);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    animation: shadow-pulse 1.25s infinite;
}

.mainbutton:hover {
    background-color: rgb(75, 107, 106);
    transform: scale(1.1);
    text-shadow: 0px 0px 6px rgba(255, 255, 255, 1);
    -webkit-box-shadow: 0px 5px 40px -10px rgba(0, 0, 0, 0.57);
    -moz-box-shadow: 0px 5px 40px -10px rgba(0, 0, 0, 0.57);
    transition: all 0.4s ease 0s;
}

.upload-form {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.sample-button {
    width: 7vw;
    height: 7vw;
    font-size: 1.1vw;
}

.delay-animation {
    animation: shadow-pulse 1.5s infinite;
}

.disable-animation {
    animation: none;
}

.legend-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
}

.color-legend {
    font-size: 1.5vw;
    font-weight: 600;
}

.blue {
    color: blue;
}

.purple {
    color: purple;
}

.green {
    color: green;
}

.red {
    color: red;
}

.move-left {
    margin-left: -15vw;
}