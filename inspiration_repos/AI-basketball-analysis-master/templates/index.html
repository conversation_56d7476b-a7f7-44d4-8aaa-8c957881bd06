{% extends "layout.html" %}

{% block content %}
    <div class="landing-container">
        <div class="main-container">
            <div class="titleandbutton">
                <div class="title">AI Basketball</div>
                <div class="titleinfo">Analyze basketball shot with machine learning</div>
                <div class="upload-form">
                    <form action="/basketball_detection" method="POST" enctype="multipart/form-data">
                        <div>
                            <div class="mainbutton" onclick="document.getElementById('image_upload').click();">Load Image</div>
                            <input type="file" style="display:none;" id="image_upload" name="image" onchange="form.submit()" />
                        </div>
                    </form>
                    <form action="/sample_detection" method="POST">
                        <button class="submit-button sample-button disable-animation" type="submit">
                            <div>Sample<br>Detection</div>
                        </button>
                    </form>
                </div>
                <div class="upload-form">
                    <form action="/shooting_analysis" method="POST" enctype="multipart/form-data">
                        <div>
                            <div class="mainbutton delay-animation"
                                onclick="document.getElementById('video_upload').click();">Load Video</div>
                            <input type="file" style="display:none;" id="video_upload" name="video" onchange="form.submit()"/>
                        </div>
                    </form>
                    <form action="/sample_analysis" method="POST">
                        <button class="submit-button sample-button disable-animation" type="submit">
                            <div>Sample<br>Analysis</div>
                        </button>
                    </form>
                </div>
            </div>
            <div class="landing-image">
                <a href="../static/img/shot_pose_analysis.png">
                    <img class="demo-image" src="../static/img/shot_pose_analysis.png">
                </a>
                <a href="../static/img/analysis_result.PNG">
                    <img class="demo-image" src="../static/img/analysis_result.PNG">
                </a>
            </div>
        </div>
        <div class="wave-button"></div>
        <div class="features-container">
            <div class="feature-title">Features</div>
            <div class="feature-row">
                <div class="big-feature">
                    <div class="feature-text">Shooting analysis</div>
                    <div class="feature-image">
                        <a href="../static/img/analysis.gif">
                            <img class="demo-feature" src="../static/img/analysis.gif">
                        </a>
                    </div>
                </div>
            </div>
            <div class="feature-row">
                <div class="small-feature">
                    <div class="feature-text">Shot detection</div>
                    <div class="feature-image">
                        <a href="../static/img/detection.PNG">
                            <img class="demo-small-feature" src="../static/img/detection.PNG">
                        </a>
                    </div>
                </div>
                <div class="small-feature">
                    <div class="feature-text">Detection API call</div>
                    <div class="feature-image">
                        <a href="../static/img/API.PNG">
                            <img class="demo-small-feature" src="../static/img/API.PNG">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
