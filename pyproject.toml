[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "basketball-analysis-pipeline"
version = "1.0.0"
description = "Production-ready basketball video analysis pipeline with API and CLI interfaces"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Basketball Analysis Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Basketball Analysis Team", email = "<EMAIL>"}
]
keywords = [
    "basketball",
    "computer-vision",
    "sports-analytics",
    "object-detection",
    "tracking",
    "machine-learning",
    "api",
    "fastapi"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Topic :: Multimedia :: Video :: Analysis",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.0.0",
    "python-multipart>=0.0.6",
    "ultralytics>=8.0.0",
    "opencv-python>=4.8.0",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "supervision>=0.16.0",
    "transformers>=4.35.0",
    "scikit-learn>=1.3.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "Pillow>=10.0.0",
    "matplotlib>=3.7.0",
    "filterpy>=1.4.5",
    "umap-learn>=0.5.4",
    "pandas>=2.0.0",
    "h5py>=3.9.0",
    "imageio>=2.31.0",
    "imageio-ffmpeg>=0.4.9",
    "boto3>=1.29.0",
    "botocore>=1.32.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "redis>=5.0.0",
    "celery>=5.3.0",
    "httpx>=0.25.0",
    "aiofiles>=23.2.0",
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.1",
    "prometheus-client>=0.18.0",
    "structlog>=23.2.0",
    "tqdm>=4.66.0",
    "click>=8.1.0",
    "rich>=13.6.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.23.0",
]
gpu = [
    "torch>=2.0.0+cu118",
    "torchvision>=0.15.0+cu118",
]
advanced = [
    "mediapipe>=0.10.0",
    "easyocr>=1.7.0",
    "pytesseract>=0.3.10",
    "av>=10.0.0",
    "onnx>=1.14.0",
    "onnxruntime>=1.16.0",
    "plotly>=5.17.0",
    "seaborn>=0.12.0",
]
production = [
    "gunicorn>=21.2.0",
    "prometheus-client>=0.18.0",
    "sentry-sdk>=1.38.0",
]

[project.urls]
Homepage = "https://github.com/your-org/basketball-analysis-pipeline"
Documentation = "https://basketball-analysis-pipeline.readthedocs.io/"
Repository = "https://github.com/your-org/basketball-analysis-pipeline.git"
"Bug Tracker" = "https://github.com/your-org/basketball-analysis-pipeline/issues"
Changelog = "https://github.com/your-org/basketball-analysis-pipeline/blob/main/CHANGELOG.md"

[project.scripts]
basketball-analyze = "scripts.cli.analyze_video:main"
basketball-api = "app.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*", "app*", "scripts*"]
exclude = ["tests*", "docs*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.txt", "*.md"]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100
known_first_party = ["src", "app", "scripts"]
known_third_party = ["fastapi", "pydantic", "ultralytics", "opencv", "torch", "numpy"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "ultralytics.*",
    "supervision.*",
    "transformers.*",
    "sklearn.*",
    "umap.*",
    "filterpy.*",
    "imageio.*",
    "boto3.*",
    "botocore.*",
    "redis.*",
    "celery.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
    "s3: marks tests that require S3 access",
]

[tool.coverage.run]
source = ["src", "app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    "venv",
    ".eggs",
    "*.egg",
]
