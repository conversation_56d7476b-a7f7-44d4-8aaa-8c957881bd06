# Basketball Analysis Pipeline - Production Architecture

This document outlines a production-ready, scalable architecture for a comprehensive basketball analysis pipeline. The system is designed to be API-first, processing videos from cloud storage (e.g., S3) and handling computation asynchronously on a GPU cluster.

---
## I. High-Level Architecture

The architecture is designed around a decoupled API and worker system to ensure responsiveness and scalability.

1.  **API Layer (`app/`)**: A lightweight FastAPI application that handles client requests. Its primary roles are to manage authentication, validate inputs, and dispatch analysis jobs to a task queue. It responds instantly with a job ID, allowing clients to track progress asynchronously.
2.  **Task Queue (Celery & Redis/RabbitMQ)**: A message broker that queues jobs submitted by the API. This decouples the API from the heavy processing, preventing timeouts and allowing for robust job management (e.g., retries).
3.  **Worker Layer (`workers/`)**: Independent, scalable processes running on the GPU cluster. Workers pull jobs from the queue, execute the core analysis pipeline, and store the results in a persistent database.
4.  **Core Library (`src/`)**: A self-contained Python package containing all the modular logic for computer vision, event detection, and analytics. It is called by the workers to perform the analysis.
5.  **Data Persistence**:
    * **File Storage (S3)**: For storing raw input videos, processed videos, and other large artifacts like heatmaps.
    * **Database (PostgreSQL)**: For storing structured data, including job statuses, detected events, and final player/team statistics.

---
## II. Directory Structure

The project is organized into distinct components to ensure modularity, maintainability, and clear separation of concerns.

```
basketball_analysis_pipeline/
├── app/                                    # FastAPI application (lightweight API)
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/
│   │       │   ├── analysis.py            # Endpoints to retrieve final results
│   │       │   ├── jobs.py                # Endpoints to create & track job status
│   │       │   └── health.py              # Health check endpoint
│   │       ├── schemas/                   # Pydantic schemas for API I/O validation
│   │       │   ├── __init__.py
│   │       │   ├── analysis.py
│   │       │   └── jobs.py
│   │       └── dependencies.py            # API dependencies (e.g., DB session)
│   ├── core/
│   │   ├── config.py                      # Pydantic settings management
│   │   └── security.py                    # Authentication & authorization logic
│   └── main.py                            # FastAPI app entry point
├── workers/                                # Background processing workers
│   ├── __init__.py
│   ├── main.py                            # Celery worker entry point
│   └── tasks.py                           # Defines the main analysis task
├── src/                                   # Core library (installable package)
│   ├── __init__.py
│   ├── analytics/                         # Analytics and statistics
│   ├── classification/                    # Team/Jersey classification
│   ├── computer_vision/                   # Homography, trajectory, etc.
│   ├── detection/                         # Object detection
│   ├── events/                            # Game event detection logic
│   ├── ocr/                               # Jersey number recognition
│   │   ├── __init__.py
│   │   ├── base_ocr.py                    # Abstract base class for OCR models
│   │   └── parseq_ocr.py                  # Concrete implementation for PARSeq
│   ├── storage/                           # Data persistence abstraction
│   │   ├── __init__.py
│   │   ├── base_storage.py                # Interfaces for storage handlers
│   │   ├── database_handler.py            # Handles PostgreSQL/SQLAlchemy operations
│   │   └── file_storage_handler.py        # Handles S3 and local file operations
│   ├── tracking/                          # Object tracking
│   ├── utils/                             # Shared utilities
│   └── visualization/                     # Video annotation and rendering
├── config/                                # Configuration files (YAML)
│   ├── settings/
│   │   ├── base.py
│   │   ├── development.py
│   │   └── production.py
│   └── models/
│       ├── detection.yaml
│       └── tracking.yaml
├── data/                                  # Local data (models, cache)
├── tests/                                 # Unit and integration tests
├── scripts/                               # CLI tools and utility scripts
├── docs/                                  # Project documentation
├── docker/
│   ├── Dockerfile.api                     # Dockerfile for the FastAPI app
│   ├── Dockerfile.worker                  # Dockerfile for the Celery worker
│   ├── docker-compose.yml                 # For local development setup
│   └── requirements/
│       ├── base.txt
│       └── production.txt
├── .env.example                           # Environment variables template
├── pyproject.toml                         # Project metadata and dependencies
└── README.md
```

