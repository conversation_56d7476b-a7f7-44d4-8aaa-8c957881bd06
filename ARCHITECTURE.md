# Basketball Analysis Pipeline - Production Architecture

This document outlines a production-ready, scalable architecture for a comprehensive basketball analysis pipeline. The system is designed to be API-first, processing videos from cloud storage (e.g., S3) and handling computation asynchronously on a GPU cluster.

---
## I. High-Level Architecture

The architecture is designed around a decoupled API and worker system to ensure responsiveness and scalability.

1.  **API Layer (`app/`)**: A lightweight FastAPI application that handles client requests. Its primary roles are to manage authentication, validate inputs, and dispatch analysis jobs to a task queue. It responds instantly with a job ID, allowing clients to track progress asynchronously.
2.  **Task Queue (Celery & Redis/RabbitMQ)**: A message broker that queues jobs submitted by the API. This decouples the API from the heavy processing, preventing timeouts and allowing for robust job management (e.g., retries).
3.  **Worker Layer (`workers/`)**: Independent, scalable processes running on the GPU cluster. Workers pull jobs from the queue, execute the core analysis pipeline, and store the results in a persistent database.
4.  **Core Library (`src/`)**: A self-contained Python package containing all the modular logic for computer vision, event detection, and analytics. It is called by the workers to perform the analysis.
5.  **Data Persistence**:
    * **File Storage (S3)**: For storing raw input videos, processed videos, and other large artifacts like heatmaps.
    * **Database (PostgreSQL)**: For storing structured data, including job statuses, detected events, and final player/team statistics.

---
## II. Directory Structure

The project is organized into distinct components to ensure modularity, maintainability, and clear separation of concerns.

```
basketball_analysis_pipeline/
├── app/                                    # FastAPI application (lightweight API)
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/
│   │       │   ├── analysis.py            # Endpoints to retrieve final results
│   │       │   ├── jobs.py                # Endpoints to create & track job status
│   │       │   └── health.py              # Health check endpoint
│   │       ├── schemas/                   # Pydantic schemas for API I/O validation
│   │       │   ├── __init__.py
│   │       │   ├── analysis.py
│   │       │   └── jobs.py
│   │       └── dependencies.py            # API dependencies (e.g., DB session)
│   ├── core/
│   │   ├── config.py                      # Pydantic settings management
│   │   └── security.py                    # Authentication & authorization logic
│   └── main.py                            # FastAPI app entry point
├── workers/                                # Background processing workers
│   ├── __init__.py
│   ├── main.py                            # Celery worker entry point
│   └── tasks.py                           # Defines the main analysis task
├── src/                                   # Core library (installable package)
│   ├── __init__.py
│   ├── analytics/                         # Analytics and statistics
│   ├── classification/                    # Team/Jersey classification
│   ├── computer_vision/                   # Homography, trajectory, etc.
│   ├── detection/                         # Object detection
│   ├── events/                            # Game event detection logic
│   ├── ocr/                               # Jersey number recognition
│   │   ├── __init__.py
│   │   ├── base_ocr.py                    # Abstract base class for OCR models
│   │   └── parseq_ocr.py                  # Concrete implementation for PARSeq
│   ├── storage/                           # Data persistence abstraction
│   │   ├── __init__.py
│   │   ├── base_storage.py                # Interfaces for storage handlers
│   │   ├── database_handler.py            # Handles PostgreSQL/SQLAlchemy operations
│   │   └── file_storage_handler.py        # Handles S3 and local file operations
│   ├── tracking/                          # Object tracking
│   ├── utils/                             # Shared utilities
│   └── visualization/                     # Video annotation and rendering
├── config/                                # Configuration files (YAML)
│   ├── settings/
│   │   ├── base.py
│   │   ├── development.py
│   │   └── production.py
│   └── models/
│       ├── detection.yaml
│       └── tracking.yaml
├── data/                                  # Local data (models, cache)
├── tests/                                 # Unit and integration tests
├── scripts/                               # CLI tools and utility scripts
├── docs/                                  # Project documentation
├── docker/
│   ├── Dockerfile.api                     # Dockerfile for the FastAPI app
│   ├── Dockerfile.worker                  # Dockerfile for the Celery worker
│   ├── docker-compose.yml                 # For local development setup
│   └── requirements/
│       ├── base.txt
│       └── production.txt
├── .env.example                           # Environment variables template
├── pyproject.toml                         # Project metadata and dependencies
└── README.md
```
---

## III. Module Responsibilities & Technology Stack

### 1. **API & Workers (`app/`, `workers/`)**
-   **Responsibilities**:
    -   `app`: Manages HTTP requests, authentication (OAuth2 with JWT), and dispatches jobs. Provides endpoints to check job status and retrieve results.
    -   `workers`: Executes the core analysis pipeline asynchronously.
-   **Technology**:
    -   **Web Framework**: FastAPI
    -   **Task Queue**: Celery
    -   **Message Broker**: Redis (for simplicity) or RabbitMQ (for robustness)

### 2. **Detection (`src/detection/`)**
-   **Responsibilities**: Detects all relevant objects in each frame.
-   **Technology**:
    -   **Player/Ref/Hoop/Ball Detection**: A single, fine-tuned **YOLOv8** model.
    -   **Court Keypoint Detection**: A specialized keypoint detection model (e.g., YOLOv8-Pose).

### 3. **Tracking (`src/tracking/`)**
-   **Responsibilities**: Assigns and maintains a unique ID for each object across frames.
-   **Technology**:
    -   **Player/Ref Tracking**: **ByteTrack** for robust multi-object tracking.
    -   **Ball Tracking**: A combination of a simple tracker and a **Kalman Filter** to handle occlusions and predict trajectory.

### 4. **Computer Vision (`src/computer_vision/`)**
-   **Responsibilities**: Establishes geometric context and performs spatial transformations.
-   **Technology**:
    -   **Homography**: **OpenCV** to calculate the perspective transformation matrix from court keypoints, mapping video coordinates to a 2D tactical view.

### 5. **Classification & OCR (`src/classification/`, `src/ocr/`)**
-   **Responsibilities**: Identifies players.
-   **Technology**:
    -   **Team Classification**: **CLIP-based zero-shot classification** on jersey crops to assign teams by color.
    -   **Jersey Number Recognition**: A dedicated OCR module using **PARSeq** on number crops to identify individual players.

### 6. **Event Detection (`src/events/`)**
-   **Responsibilities**: The core logic engine that identifies game events from tracked object data.
-   **Implementation**: Designed as a **state machine**.
    -   `possession_analyzer.py` tracks the ball's state (held by a player, in the air, etc.).
    -   Modules like `shot_detector.py` and `pass_detector.py` react to state changes to identify events like shots, scores, rebounds, assists, and steals.

### 7. **Analytics (`src/analytics/`)**
-   **Responsibilities**: Aggregates all detected events into meaningful statistics.
-   **Implementation**: After video processing is complete, this module queries the stored event log to calculate final metrics (points, assists, rebounds, FG%, etc.) for each player and generate heatmap data.

### 8. **Storage (`src/storage/`)**
-   **Responsibilities**: Abstract all data persistence operations.
-   **Technology**:
    -   **File Storage**: `boto3` for interacting with **AWS S3**.
    -   **Database**: **PostgreSQL** with **SQLAlchemy** for structured data like job info and final stats.

### 9. **Visualization (`src/visualization/`)**
-   **Responsibilities**: Creates visual outputs (optional).
-   **Implementation**: Uses **OpenCV** to draw bounding boxes, tracks, and stats on video frames. Uses **Matplotlib/Seaborn** to generate heatmap images from the analytics data.

---

## IV. Data Flow & Processing Pipeline

### Processing Workflow
```
1. Client Request → API (Job Creation)
2. Job → Message Queue → Worker Assignment
3. Worker → Video Download (S3) → Frame Extraction
4. Detection → Tracking → Classification → Event Detection
5. Analytics Calculation → Result Storage (DB + S3)
6. Client Polling → Result Retrieval
```

### Data Models
```python
# Core data structures stored in PostgreSQL
Job: {id, status, video_url, config, created_at, completed_at}
Detection: {frame_id, object_type, bbox, confidence, track_id}
Event: {timestamp, event_type, players_involved, metadata}
PlayerStats: {player_id, game_id, points, assists, rebounds, ...}
TeamStats: {team_id, game_id, possession_time, shooting_pct, ...}
```

---

## V. Scalability & Performance Considerations

### Horizontal Scaling
- **API Layer**: Stateless, can run multiple instances behind load balancer
- **Worker Layer**: Auto-scaling based on queue length and GPU availability
- **Database**: Read replicas for analytics queries, write master for job updates
- **Storage**: S3 with CloudFront CDN for video delivery

### Performance Optimizations
- **Model Optimization**: TensorRT/ONNX conversion for faster inference
- **Batch Processing**: Process multiple frames simultaneously
- **Smart Caching**: Cache detection results, homography matrices
- **Progressive Processing**: Stream results as they become available

### Resource Management
- **GPU Memory**: Dynamic allocation based on video resolution
- **Queue Prioritization**: VIP clients, urgent jobs get priority
- **Graceful Degradation**: CPU fallback when GPUs unavailable
- **Auto-scaling**: Scale workers based on queue depth and processing time

---

## VI. Monitoring & Observability

### Health Monitoring
- **API Health**: Response times, error rates, active connections
- **Worker Health**: GPU utilization, memory usage, job completion rates
- **Queue Health**: Queue depth, processing lag, failed jobs
- **Model Performance**: Inference times, accuracy metrics

### Logging Strategy
- **Structured Logging**: JSON format with correlation IDs
- **Log Levels**: DEBUG (development), INFO (production), ERROR (alerts)
- **Log Aggregation**: ELK stack or similar for centralized logging
- **Audit Trail**: Track all job state changes and user actions

### Metrics & Alerting
- **Business Metrics**: Jobs/hour, success rate, average processing time
- **Technical Metrics**: CPU/GPU usage, memory consumption, disk I/O
- **Alerting**: PagerDuty/Slack integration for critical failures
- **Dashboards**: Grafana dashboards for real-time monitoring

---

## VII. Security & Compliance

### Authentication & Authorization
- **API Authentication**: JWT tokens with expiration and refresh
- **Role-Based Access**: Admin, user, read-only roles
- **Rate Limiting**: Per-user and per-IP rate limits
- **API Key Management**: Secure key generation and rotation

### Data Security
- **Encryption**: TLS in transit, AES-256 at rest
- **Access Control**: IAM roles for S3, database access controls
- **Data Retention**: Configurable retention policies for videos/results
- **Privacy**: Optional PII scrubbing, GDPR compliance features

### Infrastructure Security
- **Network Security**: VPC, security groups, private subnets
- **Container Security**: Image scanning, non-root containers
- **Secrets Management**: AWS Secrets Manager or HashiCorp Vault
- **Backup & Recovery**: Automated backups, disaster recovery procedures

---

## VIII. Deployment & DevOps

### Container Strategy
```dockerfile
# Multi-stage builds for optimization
FROM nvidia/cuda:11.8-devel as base
FROM base as api          # Lightweight API container
FROM base as worker       # GPU-enabled worker container
FROM base as cli          # CLI tools container
```

### Orchestration
- **Kubernetes**: For production orchestration with GPU node pools
- **Docker Compose**: For local development and testing
- **Helm Charts**: For standardized K8s deployments
- **Auto-scaling**: HPA for API pods, custom scaling for GPU workers

### CI/CD Pipeline
```yaml
# GitHub Actions / GitLab CI pipeline
stages:
  - test: Unit tests, integration tests, model validation
  - build: Multi-arch container builds
  - security: Container scanning, dependency checks
  - deploy: Staging → Production with blue-green deployment
```

### Environment Management
- **Development**: Local Docker Compose with sample data
- **Staging**: Kubernetes cluster with production-like data
- **Production**: Multi-region deployment with load balancing
- **Feature Branches**: Ephemeral environments for testing

---

## IX. Cost Optimization

### Compute Optimization
- **Spot Instances**: Use spot instances for non-critical workers
- **Auto-scaling**: Scale down during low usage periods
- **GPU Sharing**: Multiple small jobs per GPU when possible
- **Preemptible Workers**: Handle interruptions gracefully

### Storage Optimization
- **S3 Intelligent Tiering**: Automatic cost optimization
- **Data Lifecycle**: Archive old videos, compress results
- **CDN Caching**: Reduce S3 egress costs
- **Database Optimization**: Partition large tables, optimize queries

### Monitoring Costs
- **Cost Alerts**: Set up billing alerts and budgets
- **Resource Tagging**: Track costs by project/customer
- **Usage Analytics**: Identify optimization opportunities
- **Reserved Capacity**: Use reserved instances for predictable workloads

---

## X. Future Enhancements

### Advanced Features
- **Real-time Processing**: Live stream analysis with WebRTC
- **Multi-camera Support**: Synchronized analysis from multiple angles
- **3D Reconstruction**: Player and ball position in 3D space
- **Predictive Analytics**: ML models for game outcome prediction

### Integration Capabilities
- **Webhook Support**: Real-time notifications to external systems
- **GraphQL API**: Flexible data querying for advanced clients
- **Mobile SDK**: Native mobile app integration
- **Third-party Integrations**: Sports data providers, broadcast systems

### AI/ML Improvements
- **Active Learning**: Improve models with user feedback
- **Federated Learning**: Train on distributed data without centralization
- **Edge Computing**: On-device processing for low latency
- **Explainable AI**: Provide reasoning for detection decisions

---

## XI. Technology Alternatives & Trade-offs

### Message Queue Alternatives
| Technology | Pros | Cons | Use Case |
|------------|------|------|----------|
| Redis | Simple, fast, built-in persistence | Single point of failure | Development, small scale |
| RabbitMQ | Robust, clustering, message durability | More complex setup | Production, high reliability |
| AWS SQS | Managed, scalable, integrated | Vendor lock-in, cost | Cloud-native, serverless |

### Database Alternatives
| Technology | Pros | Cons | Use Case |
|------------|------|------|----------|
| PostgreSQL | ACID, JSON support, mature | Single master scaling | Structured data, analytics |
| MongoDB | Flexible schema, horizontal scaling | Eventual consistency | Rapid prototyping, unstructured data |
| TimescaleDB | Time-series optimization, SQL | PostgreSQL dependency | Time-series analytics |

### Model Serving Alternatives
| Technology | Pros | Cons | Use Case |
|------------|------|------|----------|
| TorchServe | PyTorch native, auto-scaling | Limited ecosystem | PyTorch models |
| TensorRT | NVIDIA optimized, fast inference | NVIDIA GPUs only | High-performance inference |
| ONNX Runtime | Cross-platform, optimized | Model conversion required | Multi-platform deployment |