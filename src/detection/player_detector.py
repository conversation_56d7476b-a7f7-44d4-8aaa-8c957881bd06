"""
Player detection module using YOLOv8.
Detects basketball players in video frames.
"""

import time
from typing import List, Dict, Any, Optional
import numpy as np
from pathlib import Path
import logging

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    YOLO = None

from .base_detector import BaseDetector, DetectionResult, BoundingBox, ModelLoadError, InferenceError

logger = logging.getLogger(__name__)


class PlayerDetector(BaseDetector):
    """
    Player detection using YOLOv8.
    
    This detector is specifically trained to detect basketball players
    and can distinguish between players and other objects on the court.
    """
    
    def __init__(
        self,
        model_path: str = "models/detection/player_detector.pt",
        confidence_threshold: float = 0.5,
        device: str = 'auto',
        batch_size: int = 20,
        iou_threshold: float = 0.45,
        max_detections: int = 50
    ):
        """
        Initialize the player detector.
        
        Args:
            model_path: Path to YOLOv8 model weights
            confidence_threshold: Minimum confidence for detections
            device: Device for inference ('cpu', 'cuda', 'auto')
            batch_size: Batch size for inference
            iou_threshold: IoU threshold for NMS
            max_detections: Maximum number of detections per frame
        """
        super().__init__(model_path, confidence_threshold, device, batch_size)
        
        if not ULTRALYTICS_AVAILABLE:
            raise ImportError("ultralytics package is required for PlayerDetector")
        
        self.iou_threshold = iou_threshold
        self.max_detections = max_detections
        
        # Player-specific class names (based on COCO + custom classes)
        self.class_names = {
            0: 'person',
            1: 'player',  # Custom class for basketball players
            2: 'referee'  # Custom class for referees
        }
        
        # Target classes for player detection
        self.target_classes = ['person', 'player']
        
        logger.info(f"PlayerDetector initialized with IoU threshold: {iou_threshold}")
    
    def load_model(self) -> None:
        """Load the YOLOv8 model."""
        try:
            if not self.model_path.exists():
                raise ModelLoadError(f"Model file not found: {self.model_path}")
            
            logger.info(f"Loading YOLOv8 model from {self.model_path}")
            self.model = YOLO(str(self.model_path))
            
            # Move model to specified device
            if self.device != 'cpu':
                self.model.to(self.device)
            
            # Update class names from model if available
            if hasattr(self.model, 'names'):
                self.class_names.update(self.model.names)
            
            self.is_loaded = True
            logger.info(f"YOLOv8 model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load YOLOv8 model: {e}")
            raise ModelLoadError(f"Failed to load model: {e}")
    
    def detect_frame(self, frame: np.ndarray, frame_id: int) -> DetectionResult:
        """
        Detect players in a single frame.
        
        Args:
            frame: Input frame as numpy array (H, W, C)
            frame_id: Frame identifier
            
        Returns:
            DetectionResult containing player detections
        """
        if not self.is_loaded:
            self.load_model()
        
        start_time = time.time()
        
        try:
            # Run inference
            results = self.model.predict(
                frame,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                max_det=self.max_detections,
                verbose=False
            )
            
            # Process results
            detections = self._process_yolo_results(results[0], frame.shape)
            
            # Validate detections
            validated_detections = self.validate_detections(detections, frame.shape)
            
            processing_time = time.time() - start_time
            
            return DetectionResult(
                frame_id=frame_id,
                detections=validated_detections,
                processing_time=processing_time,
                metadata={
                    'model_type': 'YOLOv8',
                    'device': self.device,
                    'confidence_threshold': self.confidence_threshold,
                    'iou_threshold': self.iou_threshold
                }
            )
            
        except Exception as e:
            logger.error(f"Error detecting players in frame {frame_id}: {e}")
            raise InferenceError(f"Detection failed for frame {frame_id}: {e}")
    
    def _process_yolo_results(self, result, frame_shape: tuple) -> List[BoundingBox]:
        """
        Process YOLOv8 results into BoundingBox objects.
        
        Args:
            result: YOLOv8 result object
            frame_shape: Shape of input frame
            
        Returns:
            List of BoundingBox objects
        """
        detections = []
        
        if result.boxes is None or len(result.boxes) == 0:
            return detections
        
        # Extract detection data
        boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
        confidences = result.boxes.conf.cpu().numpy()
        class_ids = result.boxes.cls.cpu().numpy().astype(int)
        
        for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
            # Get class name
            class_name = self.class_names.get(class_id, f'class_{class_id}')
            
            # Filter for target classes (players/persons)
            if class_name not in self.target_classes:
                continue
            
            # Create bounding box
            bbox = BoundingBox(
                x1=float(box[0]),
                y1=float(box[1]),
                x2=float(box[2]),
                y2=float(box[3]),
                confidence=float(conf),
                class_id=class_id,
                class_name=class_name
            )
            
            detections.append(bbox)
        
        return detections
    
    def detect_batch(self, frames: List[np.ndarray], start_frame_id: int = 0) -> List[DetectionResult]:
        """
        Detect players in a batch of frames for improved efficiency.
        
        Args:
            frames: List of frames to process
            start_frame_id: Starting frame ID
            
        Returns:
            List of DetectionResult objects
        """
        if not self.is_loaded:
            self.load_model()
        
        start_time = time.time()
        
        try:
            # Run batch inference
            results = self.model.predict(
                frames,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                max_det=self.max_detections,
                verbose=False
            )
            
            # Process each result
            detection_results = []
            for i, (result, frame) in enumerate(zip(results, frames)):
                frame_id = start_frame_id + i
                
                # Process detections for this frame
                detections = self._process_yolo_results(result, frame.shape)
                validated_detections = self.validate_detections(detections, frame.shape)
                
                detection_results.append(DetectionResult(
                    frame_id=frame_id,
                    detections=validated_detections,
                    processing_time=(time.time() - start_time) / len(frames),  # Average time per frame
                    metadata={
                        'model_type': 'YOLOv8',
                        'device': self.device,
                        'batch_processing': True
                    }
                ))
            
            total_time = time.time() - start_time
            logger.debug(f"Processed batch of {len(frames)} frames in {total_time:.3f}s")
            
            return detection_results
            
        except Exception as e:
            logger.error(f"Error in batch detection: {e}")
            raise InferenceError(f"Batch detection failed: {e}")
    
    def filter_player_detections(
        self, 
        detections: List[BoundingBox], 
        min_area: int = 100,
        max_area: Optional[int] = None,
        aspect_ratio_range: tuple = (0.3, 3.0)
    ) -> List[BoundingBox]:
        """
        Filter player detections based on size and aspect ratio constraints.
        
        Args:
            detections: List of detections to filter
            min_area: Minimum bounding box area
            max_area: Maximum bounding box area (None for no limit)
            aspect_ratio_range: (min_ratio, max_ratio) for width/height
            
        Returns:
            Filtered list of detections
        """
        filtered = []
        
        for detection in detections:
            # Check area constraints
            area = detection.area
            if area < min_area:
                continue
            if max_area is not None and area > max_area:
                continue
            
            # Check aspect ratio
            aspect_ratio = detection.width / detection.height
            if not (aspect_ratio_range[0] <= aspect_ratio <= aspect_ratio_range[1]):
                continue
            
            filtered.append(detection)
        
        return filtered
    
    def get_player_crops(self, frame: np.ndarray, detections: List[BoundingBox]) -> List[np.ndarray]:
        """
        Extract player crops from frame based on detections.
        
        Args:
            frame: Input frame
            detections: Player detections
            
        Returns:
            List of cropped player images
        """
        crops = []
        
        for detection in detections:
            # Extract crop coordinates
            x1, y1, x2, y2 = int(detection.x1), int(detection.y1), int(detection.x2), int(detection.y2)
            
            # Ensure coordinates are within frame bounds
            h, w = frame.shape[:2]
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)
            
            # Extract crop
            if x2 > x1 and y2 > y1:
                crop = frame[y1:y2, x1:x2]
                crops.append(crop)
        
        return crops
    
    def update_model(self, new_model_path: str) -> None:
        """
        Update the model with a new weights file.
        
        Args:
            new_model_path: Path to new model weights
        """
        self.model_path = Path(new_model_path)
        self.is_loaded = False
        self.model = None
        logger.info(f"Model path updated to {new_model_path}. Model will be reloaded on next inference.")
    
    def get_detection_stats(self, detection_results: List[DetectionResult]) -> Dict[str, Any]:
        """
        Get statistics about detection results.
        
        Args:
            detection_results: List of detection results
            
        Returns:
            Dictionary containing detection statistics
        """
        total_detections = sum(len(result.detections) for result in detection_results)
        total_frames = len(detection_results)
        avg_detections_per_frame = total_detections / total_frames if total_frames > 0 else 0
        avg_processing_time = sum(result.processing_time for result in detection_results) / total_frames if total_frames > 0 else 0
        
        confidence_scores = [det.confidence for result in detection_results for det in result.detections]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        return {
            'total_frames': total_frames,
            'total_detections': total_detections,
            'avg_detections_per_frame': avg_detections_per_frame,
            'avg_processing_time': avg_processing_time,
            'avg_confidence': avg_confidence,
            'min_confidence': min(confidence_scores) if confidence_scores else 0,
            'max_confidence': max(confidence_scores) if confidence_scores else 0
        }
