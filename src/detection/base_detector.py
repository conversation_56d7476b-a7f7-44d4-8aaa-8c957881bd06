"""
Base detector interface for all object detection modules.
Provides a common interface for player, ball, and court detection.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import numpy as np
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class BoundingBox:
    """Bounding box representation."""
    x1: float
    y1: float
    x2: float
    y2: float
    confidence: float
    class_id: int
    class_name: str
    
    @property
    def center(self) -> tuple:
        """Get center point of bounding box."""
        return ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)
    
    @property
    def width(self) -> float:
        """Get width of bounding box."""
        return self.x2 - self.x1
    
    @property
    def height(self) -> float:
        """Get height of bounding box."""
        return self.y2 - self.y1
    
    @property
    def area(self) -> float:
        """Get area of bounding box."""
        return self.width * self.height
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'x1': self.x1,
            'y1': self.y1,
            'x2': self.x2,
            'y2': self.y2,
            'confidence': self.confidence,
            'class_id': self.class_id,
            'class_name': self.class_name,
            'center': self.center,
            'width': self.width,
            'height': self.height,
            'area': self.area
        }


@dataclass
class DetectionResult:
    """Detection result for a single frame."""
    frame_id: int
    detections: List[BoundingBox]
    processing_time: float
    metadata: Optional[Dict[str, Any]] = None
    
    def filter_by_confidence(self, min_confidence: float) -> 'DetectionResult':
        """Filter detections by minimum confidence threshold."""
        filtered_detections = [
            det for det in self.detections 
            if det.confidence >= min_confidence
        ]
        return DetectionResult(
            frame_id=self.frame_id,
            detections=filtered_detections,
            processing_time=self.processing_time,
            metadata=self.metadata
        )
    
    def filter_by_class(self, class_names: List[str]) -> 'DetectionResult':
        """Filter detections by class names."""
        filtered_detections = [
            det for det in self.detections 
            if det.class_name in class_names
        ]
        return DetectionResult(
            frame_id=self.frame_id,
            detections=filtered_detections,
            processing_time=self.processing_time,
            metadata=self.metadata
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'frame_id': self.frame_id,
            'detections': [det.to_dict() for det in self.detections],
            'processing_time': self.processing_time,
            'metadata': self.metadata
        }


class BaseDetector(ABC):
    """
    Abstract base class for all object detectors.
    
    This class defines the common interface that all detection modules
    (player, ball, court) must implement.
    """
    
    def __init__(
        self, 
        model_path: Union[str, Path],
        confidence_threshold: float = 0.5,
        device: str = 'auto',
        batch_size: int = 20
    ):
        """
        Initialize the base detector.
        
        Args:
            model_path: Path to the model weights file
            confidence_threshold: Minimum confidence for detections
            device: Device to run inference on ('cpu', 'cuda', 'auto')
            batch_size: Batch size for inference
        """
        self.model_path = Path(model_path)
        self.confidence_threshold = confidence_threshold
        self.device = self._determine_device(device)
        self.batch_size = batch_size
        
        # Model will be loaded lazily
        self.model = None
        self.is_loaded = False
        
        # Class names mapping
        self.class_names = {}
        
        logger.info(f"Initialized {self.__class__.__name__} with model: {self.model_path}")
    
    def _determine_device(self, device: str) -> str:
        """Determine the best device for inference."""
        if device == 'auto':
            try:
                import torch
                if torch.cuda.is_available():
                    return 'cuda'
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    return 'mps'
                else:
                    return 'cpu'
            except ImportError:
                return 'cpu'
        return device
    
    @abstractmethod
    def load_model(self) -> None:
        """Load the detection model."""
        pass
    
    @abstractmethod
    def detect_frame(self, frame: np.ndarray, frame_id: int) -> DetectionResult:
        """
        Detect objects in a single frame.
        
        Args:
            frame: Input frame as numpy array
            frame_id: Frame identifier
            
        Returns:
            DetectionResult containing all detections
        """
        pass
    
    def detect_frames(self, frames: List[np.ndarray]) -> List[DetectionResult]:
        """
        Detect objects in multiple frames.
        
        Args:
            frames: List of input frames
            
        Returns:
            List of DetectionResult objects
        """
        if not self.is_loaded:
            self.load_model()
        
        results = []
        
        # Process frames in batches
        for i in range(0, len(frames), self.batch_size):
            batch = frames[i:i + self.batch_size]
            batch_results = self._detect_batch(batch, start_frame_id=i)
            results.extend(batch_results)
        
        logger.info(f"Processed {len(frames)} frames, found {sum(len(r.detections) for r in results)} detections")
        return results
    
    def _detect_batch(self, batch: List[np.ndarray], start_frame_id: int) -> List[DetectionResult]:
        """
        Detect objects in a batch of frames.
        
        Args:
            batch: Batch of frames
            start_frame_id: Starting frame ID for this batch
            
        Returns:
            List of DetectionResult objects
        """
        results = []
        for i, frame in enumerate(batch):
            frame_id = start_frame_id + i
            result = self.detect_frame(frame, frame_id)
            results.append(result)
        return results
    
    def _post_process_detections(self, raw_detections: Any, frame_id: int) -> DetectionResult:
        """
        Post-process raw model outputs into DetectionResult format.
        
        Args:
            raw_detections: Raw model output
            frame_id: Frame identifier
            
        Returns:
            Processed DetectionResult
        """
        # This method should be implemented by subclasses
        # based on their specific model output format
        raise NotImplementedError("Subclasses must implement _post_process_detections")
    
    def validate_detections(self, detections: List[BoundingBox], frame_shape: tuple) -> List[BoundingBox]:
        """
        Validate and clean detection results.
        
        Args:
            detections: List of detections to validate
            frame_shape: Shape of the input frame (height, width, channels)
            
        Returns:
            List of validated detections
        """
        validated = []
        height, width = frame_shape[:2]
        
        for detection in detections:
            # Check confidence threshold
            if detection.confidence < self.confidence_threshold:
                continue
            
            # Clamp coordinates to frame boundaries
            detection.x1 = max(0, min(detection.x1, width))
            detection.y1 = max(0, min(detection.y1, height))
            detection.x2 = max(0, min(detection.x2, width))
            detection.y2 = max(0, min(detection.y2, height))
            
            # Check for valid bounding box
            if detection.x2 <= detection.x1 or detection.y2 <= detection.y1:
                continue
            
            # Check minimum size
            if detection.width < 5 or detection.height < 5:
                continue
            
            validated.append(detection)
        
        return validated
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            'model_path': str(self.model_path),
            'confidence_threshold': self.confidence_threshold,
            'device': self.device,
            'batch_size': self.batch_size,
            'is_loaded': self.is_loaded,
            'class_names': self.class_names
        }
    
    def update_confidence_threshold(self, new_threshold: float) -> None:
        """Update the confidence threshold."""
        if 0.0 <= new_threshold <= 1.0:
            self.confidence_threshold = new_threshold
            logger.info(f"Updated confidence threshold to {new_threshold}")
        else:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
    
    def __repr__(self) -> str:
        return (f"{self.__class__.__name__}("
                f"model_path='{self.model_path}', "
                f"confidence_threshold={self.confidence_threshold}, "
                f"device='{self.device}', "
                f"is_loaded={self.is_loaded})")


class DetectionError(Exception):
    """Custom exception for detection-related errors."""
    pass


class ModelLoadError(DetectionError):
    """Exception raised when model loading fails."""
    pass


class InferenceError(DetectionError):
    """Exception raised during inference."""
    pass
