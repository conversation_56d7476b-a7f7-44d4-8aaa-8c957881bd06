"""
Analysis endpoints for basketball video processing.
Handles video analysis requests from S3, direct upload, and URL sources.
"""

import asyncio
import uuid
from typing import Optional, Dict, Any, List
from pathlib import Path

from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks, Depends, Query
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, HttpUrl, validator

from ...core.config import get_settings
from ...core.exceptions import ValidationError, ProcessingError
from ....main import get_job_manager
from src.core.pipeline import BasketballAnalysisPipeline, ProcessingConfig
from src.core.job_manager import JobManager, JobStatus
from src.utils.video_io.s3_handler import S3Handler
from src.utils.video_io.video_reader import VideoReader

router = APIRouter()
settings = get_settings()


class S3VideoRequest(BaseModel):
    """Request model for S3 video analysis."""
    s3_bucket: str
    s3_key: str
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_region: str = "us-east-1"
    
    # Processing configuration
    player_detection_confidence: float = 0.5
    ball_detection_confidence: float = 0.3
    court_detection_confidence: float = 0.6
    team_classification_enabled: bool = True
    team_colors: Optional[List[str]] = None
    generate_annotated_video: bool = True
    generate_tactical_view: bool = True
    export_json_stats: bool = True
    
    @validator('player_detection_confidence', 'ball_detection_confidence', 'court_detection_confidence')
    def validate_confidence(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Confidence must be between 0.0 and 1.0')
        return v


class URLVideoRequest(BaseModel):
    """Request model for URL video analysis."""
    video_url: HttpUrl
    
    # Processing configuration (same as S3VideoRequest)
    player_detection_confidence: float = 0.5
    ball_detection_confidence: float = 0.3
    court_detection_confidence: float = 0.6
    team_classification_enabled: bool = True
    team_colors: Optional[List[str]] = None
    generate_annotated_video: bool = True
    generate_tactical_view: bool = True
    export_json_stats: bool = True


class AnalysisResponse(BaseModel):
    """Response model for analysis requests."""
    job_id: str
    status: str
    message: str
    estimated_processing_time: Optional[float] = None
    progress_url: str
    result_url: str


class AnalysisResult(BaseModel):
    """Complete analysis result model."""
    job_id: str
    status: str
    processing_time: float
    video_info: Dict[str, Any]
    
    # Detection results
    player_detections_count: int
    ball_detections_count: int
    court_detections_count: int
    
    # Tracking results
    unique_players_tracked: int
    ball_tracking_accuracy: float
    
    # Event detection results
    shots_detected: int
    passes_detected: int
    possessions: List[Dict[str, Any]]
    
    # Analytics
    player_stats: Dict[str, Any]
    team_stats: Dict[str, Any]
    
    # Output files
    annotated_video_url: Optional[str] = None
    tactical_view_url: Optional[str] = None
    json_stats_url: Optional[str] = None
    
    # Metadata
    errors: Optional[List[str]] = None


@router.post("/s3", response_model=AnalysisResponse)
async def analyze_s3_video(
    request: S3VideoRequest,
    background_tasks: BackgroundTasks,
    job_manager: JobManager = Depends(get_job_manager)
):
    """
    Analyze a basketball video stored in S3.
    
    Args:
        request: S3 video analysis request
        background_tasks: FastAPI background tasks
        job_manager: Job manager dependency
        
    Returns:
        Analysis response with job information
    """
    try:
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Validate S3 access
        s3_handler = S3Handler(
            aws_access_key_id=request.aws_access_key_id,
            aws_secret_access_key=request.aws_secret_access_key,
            region=request.aws_region
        )
        
        # Check if video exists
        if not await s3_handler.object_exists(request.s3_bucket, request.s3_key):
            raise HTTPException(
                status_code=404,
                detail=f"Video not found: s3://{request.s3_bucket}/{request.s3_key}"
            )
        
        # Get video info for time estimation
        video_info = await s3_handler.get_object_info(request.s3_bucket, request.s3_key)
        estimated_time = _estimate_processing_time(video_info.get('size', 0))
        
        # Create processing configuration
        config = ProcessingConfig(
            player_detection_confidence=request.player_detection_confidence,
            ball_detection_confidence=request.ball_detection_confidence,
            court_detection_confidence=request.court_detection_confidence,
            team_classification_enabled=request.team_classification_enabled,
            team_colors=request.team_colors,
            generate_annotated_video=request.generate_annotated_video,
            generate_tactical_view=request.generate_tactical_view,
            export_json_stats=request.export_json_stats
        )
        
        # Submit job
        video_source = f"s3://{request.s3_bucket}/{request.s3_key}"
        await job_manager.submit_job(
            job_id=job_id,
            video_source=video_source,
            config=config,
            s3_credentials={
                'aws_access_key_id': request.aws_access_key_id,
                'aws_secret_access_key': request.aws_secret_access_key,
                'aws_region': request.aws_region
            }
        )
        
        return AnalysisResponse(
            job_id=job_id,
            status="queued",
            message="Video analysis job submitted successfully",
            estimated_processing_time=estimated_time,
            progress_url=f"/api/v1/jobs/{job_id}/progress",
            result_url=f"/api/v1/jobs/{job_id}/result"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to submit analysis job: {str(e)}")


@router.post("/upload", response_model=AnalysisResponse)
async def analyze_uploaded_video(
    video_file: UploadFile = File(...),
    player_detection_confidence: float = Query(0.5, ge=0.0, le=1.0),
    ball_detection_confidence: float = Query(0.3, ge=0.0, le=1.0),
    court_detection_confidence: float = Query(0.6, ge=0.0, le=1.0),
    team_classification_enabled: bool = Query(True),
    generate_annotated_video: bool = Query(True),
    generate_tactical_view: bool = Query(True),
    export_json_stats: bool = Query(True),
    job_manager: JobManager = Depends(get_job_manager)
):
    """
    Analyze an uploaded basketball video file.
    
    Args:
        video_file: Uploaded video file
        Various processing parameters
        job_manager: Job manager dependency
        
    Returns:
        Analysis response with job information
    """
    try:
        # Validate file type
        if not _is_valid_video_file(video_file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported video format. Supported formats: {settings.supported_video_formats}"
            )
        
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Save uploaded file
        upload_dir = Path(settings.upload_directory)
        upload_dir.mkdir(exist_ok=True)
        
        file_path = upload_dir / f"{job_id}_{video_file.filename}"
        
        with open(file_path, "wb") as buffer:
            content = await video_file.read()
            buffer.write(content)
        
        # Estimate processing time
        estimated_time = _estimate_processing_time(len(content))
        
        # Create processing configuration
        config = ProcessingConfig(
            player_detection_confidence=player_detection_confidence,
            ball_detection_confidence=ball_detection_confidence,
            court_detection_confidence=court_detection_confidence,
            team_classification_enabled=team_classification_enabled,
            generate_annotated_video=generate_annotated_video,
            generate_tactical_view=generate_tactical_view,
            export_json_stats=export_json_stats
        )
        
        # Submit job
        await job_manager.submit_job(
            job_id=job_id,
            video_source=str(file_path),
            config=config
        )
        
        return AnalysisResponse(
            job_id=job_id,
            status="queued",
            message="Video analysis job submitted successfully",
            estimated_processing_time=estimated_time,
            progress_url=f"/api/v1/jobs/{job_id}/progress",
            result_url=f"/api/v1/jobs/{job_id}/result"
        )
        
    except Exception as e:
        # Clean up uploaded file on error
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        raise HTTPException(status_code=500, detail=f"Failed to process uploaded video: {str(e)}")


@router.post("/url", response_model=AnalysisResponse)
async def analyze_video_url(
    request: URLVideoRequest,
    job_manager: JobManager = Depends(get_job_manager)
):
    """
    Analyze a basketball video from a URL.
    
    Args:
        request: URL video analysis request
        job_manager: Job manager dependency
        
    Returns:
        Analysis response with job information
    """
    try:
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Validate URL accessibility
        video_reader = VideoReader(str(request.video_url))
        if not await video_reader.validate_source():
            raise HTTPException(
                status_code=400,
                detail=f"Video URL is not accessible: {request.video_url}"
            )
        
        # Create processing configuration
        config = ProcessingConfig(
            player_detection_confidence=request.player_detection_confidence,
            ball_detection_confidence=request.ball_detection_confidence,
            court_detection_confidence=request.court_detection_confidence,
            team_classification_enabled=request.team_classification_enabled,
            team_colors=request.team_colors,
            generate_annotated_video=request.generate_annotated_video,
            generate_tactical_view=request.generate_tactical_view,
            export_json_stats=request.export_json_stats
        )
        
        # Submit job
        await job_manager.submit_job(
            job_id=job_id,
            video_source=str(request.video_url),
            config=config
        )
        
        return AnalysisResponse(
            job_id=job_id,
            status="queued",
            message="Video analysis job submitted successfully",
            progress_url=f"/api/v1/jobs/{job_id}/progress",
            result_url=f"/api/v1/jobs/{job_id}/result"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to submit URL analysis job: {str(e)}")


def _is_valid_video_file(filename: str) -> bool:
    """Check if the uploaded file has a valid video extension."""
    if not filename:
        return False
    
    extension = Path(filename).suffix.lower()
    return extension in settings.supported_video_formats


def _estimate_processing_time(file_size_bytes: int) -> float:
    """
    Estimate processing time based on file size.
    
    Args:
        file_size_bytes: Size of video file in bytes
        
    Returns:
        Estimated processing time in seconds
    """
    # Rough estimation: 1MB per second of processing
    # This should be calibrated based on actual performance
    mb_size = file_size_bytes / (1024 * 1024)
    base_time = mb_size * 1.0  # 1 second per MB
    
    # Add overhead for different processing stages
    overhead_factor = 2.5  # Account for detection, tracking, analytics
    
    return base_time * overhead_factor
