"""
FastAPI application for basketball analysis pipeline.
Provides REST API endpoints for video analysis with S3 and direct upload support.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .api.v1.endpoints import analysis, jobs, health
from .api.middleware.logging import LoggingMiddleware
from .core.config import get_settings
from .core.exceptions import BasketballAnalysisException
from src.core.job_manager import JobManager
from src.utils.model_management.model_loader import ModelLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global job manager instance
job_manager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    Handles startup and shutdown events.
    """
    # Startup
    logger.info("Starting Basketball Analysis API")
    
    # Initialize job manager
    global job_manager
    settings = get_settings()
    job_manager = JobManager(
        max_concurrent_jobs=settings.max_concurrent_jobs,
        job_timeout=settings.job_timeout
    )
    
    # Pre-load models if configured
    if settings.preload_models:
        logger.info("Pre-loading models...")
        model_loader = ModelLoader()
        await model_loader.preload_models()
        logger.info("Models pre-loaded successfully")
    
    # Start job manager
    await job_manager.start()
    logger.info("Job manager started")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Basketball Analysis API")
    if job_manager:
        await job_manager.stop()
    logger.info("Shutdown complete")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    settings = get_settings()
    
    app = FastAPI(
        title="Basketball Analysis API",
        description="Production-ready API for basketball video analysis",
        version="1.0.0",
        docs_url="/docs" if settings.environment != "production" else None,
        redoc_url="/redoc" if settings.environment != "production" else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(LoggingMiddleware)
    
    # Include routers
    app.include_router(
        health.router,
        prefix="/health",
        tags=["health"]
    )
    
    app.include_router(
        analysis.router,
        prefix="/api/v1/analysis",
        tags=["analysis"]
    )
    
    app.include_router(
        jobs.router,
        prefix="/api/v1/jobs",
        tags=["jobs"]
    )
    
    # Global exception handler
    @app.exception_handler(BasketballAnalysisException)
    async def basketball_analysis_exception_handler(request, exc: BasketballAnalysisException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_type,
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "internal_server_error",
                "message": "An internal server error occurred"
            }
        )
    
    return app


def get_job_manager() -> JobManager:
    """Dependency to get the job manager instance."""
    if job_manager is None:
        raise HTTPException(status_code=503, detail="Job manager not initialized")
    return job_manager


# Create the app instance
app = create_app()


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Basketball Analysis API",
        "version": "1.0.0",
        "description": "Production-ready API for basketball video analysis",
        "docs_url": "/docs",
        "health_check": "/health"
    }


@app.get("/info")
async def info():
    """Get API information and status."""
    settings = get_settings()
    return {
        "api_version": "1.0.0",
        "environment": settings.environment,
        "max_concurrent_jobs": settings.max_concurrent_jobs,
        "supported_formats": ["mp4", "avi", "mov", "mkv"],
        "features": [
            "player_detection",
            "ball_detection", 
            "player_tracking",
            "ball_tracking",
            "team_classification",
            "shot_detection",
            "pass_detection",
            "analytics",
            "tactical_view"
        ]
    }


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.environment == "development",
        log_level=settings.log_level.lower(),
        workers=1 if settings.environment == "development" else settings.workers
    )
