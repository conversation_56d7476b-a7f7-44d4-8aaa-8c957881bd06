"""
Configuration management for the basketball analysis API.
Handles environment-specific settings and model configurations.
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path
from functools import lru_cache

from pydantic import BaseSettings, validator
from pydantic.env_settings import SettingsSourceCallable


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    """
    
    # Application settings
    app_name: str = "Basketball Analysis API"
    environment: str = "development"
    debug: bool = False
    version: str = "1.0.0"
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    log_level: str = "INFO"
    
    # Security settings
    secret_key: str = "your-secret-key-change-in-production"
    api_key_header: str = "X-API-Key"
    allowed_origins: List[str] = ["*"]
    
    # Processing settings
    max_concurrent_jobs: int = 3
    job_timeout: int = 3600  # 1 hour
    max_video_size_mb: int = 500
    supported_video_formats: List[str] = [".mp4", ".avi", ".mov", ".mkv", ".webm"]
    
    # Storage settings
    upload_directory: str = "data/uploads"
    output_directory: str = "data/outputs"
    cache_directory: str = "data/cache"
    models_directory: str = "data/models"
    
    # Model settings
    preload_models: bool = False
    model_cache_size: int = 3
    detection_batch_size: int = 20
    tracking_batch_size: int = 50
    
    # GPU settings
    use_gpu: bool = True
    gpu_memory_fraction: float = 0.8
    
    # S3 settings
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_default_region: str = "us-east-1"
    s3_bucket_name: Optional[str] = None
    
    # Database settings (for job tracking)
    database_url: str = "sqlite:///./basketball_analysis.db"
    
    # Redis settings (for caching and job queue)
    redis_url: Optional[str] = None
    redis_password: Optional[str] = None
    
    # Monitoring settings
    enable_metrics: bool = True
    metrics_port: int = 9090
    log_requests: bool = True
    
    # Model paths
    player_detection_model: str = "models/detection/player_detector.pt"
    ball_detection_model: str = "models/detection/ball_detector.pt"
    court_detection_model: str = "models/detection/court_detector.pt"
    team_classification_model: str = "models/classification/team_classifier.pt"
    
    # Processing defaults
    default_player_confidence: float = 0.5
    default_ball_confidence: float = 0.3
    default_court_confidence: float = 0.6
    default_iou_threshold: float = 0.45
    
    # Analytics settings
    court_width_meters: float = 28.0  # NBA court width
    court_height_meters: float = 15.0  # NBA court height
    fps_for_speed_calculation: float = 30.0
    
    # Output settings
    output_video_quality: str = "high"  # low, medium, high
    output_video_fps: int = 30
    tactical_view_resolution: tuple = (1920, 1080)
    
    @validator('environment')
    def validate_environment(cls, v):
        if v not in ['development', 'staging', 'production']:
            raise ValueError('Environment must be development, staging, or production')
        return v
    
    @validator('max_concurrent_jobs')
    def validate_max_concurrent_jobs(cls, v):
        if v < 1 or v > 10:
            raise ValueError('max_concurrent_jobs must be between 1 and 10')
        return v
    
    @validator('gpu_memory_fraction')
    def validate_gpu_memory_fraction(cls, v):
        if not 0.1 <= v <= 1.0:
            raise ValueError('gpu_memory_fraction must be between 0.1 and 1.0')
        return v
    
    @validator('supported_video_formats')
    def validate_video_formats(cls, v):
        # Ensure all formats start with a dot
        return [fmt if fmt.startswith('.') else f'.{fmt}' for fmt in v]
    
    def get_model_path(self, model_type: str) -> Path:
        """Get the full path for a model file."""
        model_paths = {
            'player_detection': self.player_detection_model,
            'ball_detection': self.ball_detection_model,
            'court_detection': self.court_detection_model,
            'team_classification': self.team_classification_model
        }
        
        if model_type not in model_paths:
            raise ValueError(f"Unknown model type: {model_type}")
        
        return Path(self.models_directory) / model_paths[model_type]
    
    def get_processing_config(self) -> Dict[str, Any]:
        """Get default processing configuration."""
        return {
            'player_detection_confidence': self.default_player_confidence,
            'ball_detection_confidence': self.default_ball_confidence,
            'court_detection_confidence': self.default_court_confidence,
            'iou_threshold': self.default_iou_threshold,
            'batch_size': self.detection_batch_size,
            'use_gpu': self.use_gpu,
            'court_dimensions': {
                'width_meters': self.court_width_meters,
                'height_meters': self.court_height_meters
            }
        }
    
    def create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.upload_directory,
            self.output_directory,
            self.cache_directory,
            self.models_directory
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class DevelopmentSettings(Settings):
    """Development environment settings."""
    environment: str = "development"
    debug: bool = True
    log_level: str = "DEBUG"
    preload_models: bool = False
    max_concurrent_jobs: int = 2


class ProductionSettings(Settings):
    """Production environment settings."""
    environment: str = "production"
    debug: bool = False
    log_level: str = "INFO"
    preload_models: bool = True
    max_concurrent_jobs: int = 5
    workers: int = 4
    
    # Security
    allowed_origins: List[str] = []  # Should be set explicitly in production
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        if v == "your-secret-key-change-in-production":
            raise ValueError('Secret key must be changed in production')
        return v


class TestingSettings(Settings):
    """Testing environment settings."""
    environment: str = "testing"
    debug: bool = True
    log_level: str = "DEBUG"
    database_url: str = "sqlite:///./test_basketball_analysis.db"
    upload_directory: str = "test_data/uploads"
    output_directory: str = "test_data/outputs"
    cache_directory: str = "test_data/cache"
    max_concurrent_jobs: int = 1
    preload_models: bool = False


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings based on environment.
    
    Returns:
        Settings instance for current environment
    """
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    if environment == "production":
        settings = ProductionSettings()
    elif environment == "testing":
        settings = TestingSettings()
    else:
        settings = DevelopmentSettings()
    
    # Create necessary directories
    settings.create_directories()
    
    return settings


def get_model_config() -> Dict[str, Any]:
    """
    Get model configuration settings.
    
    Returns:
        Dictionary containing model configurations
    """
    settings = get_settings()
    
    return {
        'detection_models': {
            'player': {
                'path': settings.get_model_path('player_detection'),
                'confidence_threshold': settings.default_player_confidence,
                'iou_threshold': settings.default_iou_threshold,
                'batch_size': settings.detection_batch_size
            },
            'ball': {
                'path': settings.get_model_path('ball_detection'),
                'confidence_threshold': settings.default_ball_confidence,
                'iou_threshold': settings.default_iou_threshold,
                'batch_size': settings.detection_batch_size
            },
            'court': {
                'path': settings.get_model_path('court_detection'),
                'confidence_threshold': settings.default_court_confidence,
                'batch_size': settings.detection_batch_size
            }
        },
        'tracking_models': {
            'player_tracker': {
                'max_age': 30,
                'min_hits': 3,
                'iou_threshold': 0.3
            },
            'ball_tracker': {
                'buffer_size': 10,
                'smoothing_factor': 0.8
            }
        },
        'classification_models': {
            'team_classifier': {
                'path': settings.get_model_path('team_classification'),
                'batch_size': 32,
                'confidence_threshold': 0.7
            }
        },
        'processing': {
            'use_gpu': settings.use_gpu,
            'gpu_memory_fraction': settings.gpu_memory_fraction,
            'court_dimensions': {
                'width_meters': settings.court_width_meters,
                'height_meters': settings.court_height_meters
            }
        }
    }


def get_api_config() -> Dict[str, Any]:
    """
    Get API configuration settings.
    
    Returns:
        Dictionary containing API configurations
    """
    settings = get_settings()
    
    return {
        'server': {
            'host': settings.host,
            'port': settings.port,
            'workers': settings.workers,
            'log_level': settings.log_level
        },
        'security': {
            'api_key_header': settings.api_key_header,
            'allowed_origins': settings.allowed_origins
        },
        'processing': {
            'max_concurrent_jobs': settings.max_concurrent_jobs,
            'job_timeout': settings.job_timeout,
            'max_video_size_mb': settings.max_video_size_mb,
            'supported_formats': settings.supported_video_formats
        },
        'storage': {
            'upload_directory': settings.upload_directory,
            'output_directory': settings.output_directory,
            'cache_directory': settings.cache_directory
        }
    }
