# Core dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.0.0
python-multipart>=0.0.6

# Computer Vision and ML
ultralytics>=8.0.0
opencv-python>=4.8.0
torch>=2.0.0
torchvision>=0.15.0
supervision>=0.16.0
transformers>=4.35.0
scikit-learn>=1.3.0
numpy>=1.24.0
scipy>=1.11.0

# Image Processing
Pillow>=10.0.0
matplotlib>=3.7.0

# Tracking and Analytics
filterpy>=1.4.5
umap-learn>=0.5.4

# Data Processing
pandas>=2.0.0
h5py>=3.9.0

# Video Processing
imageio>=2.31.0
imageio-ffmpeg>=0.4.9

# AWS Integration
boto3>=1.29.0
botocore>=1.32.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
sqlite3  # Built-in with Python

# Caching and Queue
redis>=5.0.0
celery>=5.3.0

# HTTP and Networking
httpx>=0.25.0
aiofiles>=23.2.0
requests>=2.31.0

# Configuration and Environment
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Monitoring and Logging
prometheus-client>=0.18.0
structlog>=23.2.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Utilities
tqdm>=4.66.0
click>=8.1.0
rich>=13.6.0
typer>=0.9.0

# Optional GPU acceleration
# Uncomment if using CUDA
# torch-audio>=2.0.0+cu118
# torchaudio>=2.0.0+cu118

# Optional dependencies for specific features
# Uncomment as needed

# For advanced pose estimation
# mediapipe>=0.10.0

# For OCR capabilities
# easyocr>=1.7.0
# pytesseract>=0.3.10

# For advanced video codecs
# av>=10.0.0

# For distributed processing
# ray>=2.7.0

# For model optimization
# onnx>=1.14.0
# onnxruntime>=1.16.0

# For advanced visualization
# plotly>=5.17.0
# seaborn>=0.12.0

# Production deployment
gunicorn>=21.2.0
